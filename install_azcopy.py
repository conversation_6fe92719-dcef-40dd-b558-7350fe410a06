#!/usr/bin/env python3
"""
Script pour télécharger et installer AzCopy automatiquement
"""

import os
import sys
import requests
import zipfile
import shutil
from pathlib import Path

def download_azcopy():
    """Télécharge et installe AzCopy pour Windows"""
    
    print("🔄 Téléchargement d'AzCopy...")
    
    # URL pour AzCopy Windows
    azcopy_url = "https://aka.ms/downloadazcopy-v10-windows"
    
    # Dossier de destination
    install_dir = Path("./azcopy")
    install_dir.mkdir(exist_ok=True)
    
    # Télécharger le fichier zip
    zip_path = install_dir / "azcopy.zip"
    
    try:
        response = requests.get(azcopy_url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(zip_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📦 Téléchargement: {progress:.1f}%", end="", flush=True)
        
        print("\n✅ Téléchargement terminé!")
        
        # Extraire le fichier zip
        print("📂 Extraction d'AzCopy...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(install_dir)
        
        # Trouver l'exécutable azcopy.exe
        azcopy_exe = None
        for root, dirs, files in os.walk(install_dir):
            for file in files:
                if file == "azcopy.exe":
                    azcopy_exe = Path(root) / file
                    break
            if azcopy_exe:
                break
        
        if azcopy_exe:
            # Copier azcopy.exe dans le dossier principal
            final_path = Path("./azcopy.exe")
            shutil.copy2(azcopy_exe, final_path)
            
            # Nettoyer les fichiers temporaires
            shutil.rmtree(install_dir)
            
            print(f"✅ AzCopy installé avec succès: {final_path.absolute()}")
            
            # Tester l'installation
            import subprocess
            result = subprocess.run([str(final_path), "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"🎉 Version AzCopy: {result.stdout.strip()}")
                return str(final_path.absolute())
            else:
                print("❌ Erreur lors du test d'AzCopy")
                return None
        else:
            print("❌ Impossible de trouver azcopy.exe dans l'archive")
            return None
            
    except Exception as e:
        print(f"❌ Erreur lors du téléchargement d'AzCopy: {e}")
        return None

def check_azcopy():
    """Vérifie si AzCopy est disponible"""
    
    # Vérifier si azcopy.exe existe localement
    local_azcopy = Path("./azcopy.exe")
    if local_azcopy.exists():
        print(f"✅ AzCopy trouvé localement: {local_azcopy.absolute()}")
        return str(local_azcopy.absolute())
    
    # Vérifier si azcopy est dans le PATH
    import subprocess
    try:
        result = subprocess.run(["azcopy", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ AzCopy trouvé dans le PATH système")
            return "azcopy"
    except FileNotFoundError:
        pass
    
    print("⚠️ AzCopy non trouvé")
    return None

def main():
    print("🔍 Vérification d'AzCopy...")
    
    azcopy_path = check_azcopy()
    
    if not azcopy_path:
        print("📥 Installation d'AzCopy...")
        azcopy_path = download_azcopy()
        
        if not azcopy_path:
            print("❌ Échec de l'installation d'AzCopy")
            sys.exit(1)
    
    print(f"🎯 AzCopy prêt à utiliser: {azcopy_path}")
    return azcopy_path

if __name__ == "__main__":
    main()
