#!/usr/bin/env python3
"""
Script de test pour vérifier les options du script de téléchargement
sans effectuer de téléchargement réel.
"""

import argparse
from omegaconf import OmegaConf

def test_script_options():
    parser = argparse.ArgumentParser(description="Test script options")
    parser.add_argument("--baseline", type=str, default="./boeing_baseline_3.yml")
    parser.add_argument("--verbose", "-v", action="store_true")
    parser.add_argument("--skip-testinference-filter", action="store_true")
    args = parser.parse_args()
    
    print("🧪 Testing script options...")
    print(f"📁 Baseline: {args.baseline}")
    print(f"🔧 Verbose: {args.verbose}")
    print(f"🔧 Skip testinference filter: {args.skip_testinference_filter}")
    print("-" * 60)
    
    # Load configuration
    cfg = OmegaConf.load(args.baseline)
    splits = list(cfg.keys())
    baseline_name = args.baseline.split('/')[-1][:-4]
    
    print(f"📊 Baseline name: {baseline_name}")
    print(f"📊 Splits found: {splits}")
    print("-" * 60)
    
    # Calculate total assets
    total_assets = 0
    for split in splits:
        if cfg[split] is not None and len(cfg[split]) > 0:
            split_count = len(cfg[split])
            if split == 'testinference' and not args.skip_testinference_filter:
                # Only count assets from index 17+
                filtered_count = max(0, split_count - 17)
                print(f"📦 {split}: {split_count} total assets, {filtered_count} would be downloaded (filter: index >= 17)")
                total_assets += filtered_count
            else:
                print(f"📦 {split}: {split_count} assets would be downloaded")
                total_assets += split_count
        else:
            print(f"📦 {split}: empty or None")
    
    print("-" * 60)
    print(f"📊 Total assets that would be downloaded: {total_assets}")
    
    # Show first few assets from each split
    for split in splits:
        if cfg[split] is not None and len(cfg[split]) > 0:
            print(f"\n🔍 First 3 assets in {split}:")
            for i, asset in enumerate(cfg[split][:3]):
                should_download = True
                if split == 'testinference' and not args.skip_testinference_filter and i < 17:
                    should_download = False
                status = "✅ Would download" if should_download else "⏭️ Would skip"
                print(f"  [{i+1}] {asset} - {status}")
            
            if len(cfg[split]) > 3:
                print(f"  ... and {len(cfg[split]) - 3} more assets")

if __name__ == "__main__":
    test_script_options()
