#!/usr/bin/env python3
"""
Script de téléchargement Azure ML utilisant AzCopy pour éviter les avertissements
et améliorer les performances.
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path
from azure.identity import InteractiveBrowserCredential
from azure.ai.ml import MLClient
from azure.ai.ml._utils._storage_utils import get_ds_name_and_path_prefix
from omegaconf import OmegaConf

def find_azcopy():
    """Trouve l'exécutable AzCopy"""
    
    # Vérifier si azcopy.exe existe localement
    local_azcopy = Path("./azcopy.exe")
    if local_azcopy.exists():
        return str(local_azcopy.absolute())
    
    # Vérifier si azcopy est dans le PATH
    try:
        result = subprocess.run(["azcopy", "--version"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            return "azcopy"
    except FileNotFoundError:
        pass
    
    return None

def get_blob_url_with_sas(ml_client, asset_name, asset_version, verbose=False):
    """Obtient l'URL de blob storage avec token SAS pour un asset"""

    try:
        # Obtenir les informations de l'asset
        dataobject = ml_client.data.get(asset_name, version=asset_version)
        base_path = dataobject.path

        if verbose:
            print(f"  🔗 Azure ML URI: {base_path}")

        # Extraire les informations du datastore
        from azure.ai.ml._utils._storage_utils import get_ds_name_and_path_prefix
        (ds_name, path_prefix) = get_ds_name_and_path_prefix(base_path)

        # Obtenir le datastore
        datastore = ml_client.datastores.get(ds_name)

        # Construire l'URL de blob storage
        account_name = datastore.account_name
        container_name = datastore.container_name

        # URL de base du blob storage
        blob_url = f"https://{account_name}.blob.core.windows.net/{container_name}/{path_prefix}"

        if verbose:
            print(f"  🔗 Blob URL: {blob_url}")

        return blob_url, datastore

    except Exception as e:
        if verbose:
            print(f"  ❌ Erreur lors de l'obtention de l'URL blob: {e}")
        return None, None

def download_with_azcopy(blob_url, destination, azcopy_path, verbose=False):
    """Télécharge un dataset en utilisant AzCopy avec URL de blob storage"""

    try:
        # Construire la commande AzCopy
        cmd = [
            azcopy_path,
            "copy",
            blob_url,
            destination,
            "--recursive",
            "--overwrite=ifSourceNewer"
        ]

        if not verbose:
            cmd.append("--output-type=essential")

        # Exécuter AzCopy
        if verbose:
            print(f"  🔄 Commande AzCopy: {' '.join(cmd[:3])} [URL] {' '.join(cmd[4:])}")

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            if verbose:
                print(f"  ✅ Téléchargement réussi")
                if result.stdout:
                    # Afficher seulement les lignes importantes
                    for line in result.stdout.split('\n'):
                        if 'completed' in line.lower() or 'transferred' in line.lower():
                            print(f"    📊 {line.strip()}")
            return True
        else:
            if verbose:
                print(f"  ❌ Erreur AzCopy: {result.stderr}")
            return False

    except Exception as e:
        print(f"  ❌ Erreur lors de l'exécution d'AzCopy: {e}")
        return False

def main(args):
    
    # Vérifier AzCopy
    azcopy_path = find_azcopy()
    if not azcopy_path:
        print("❌ AzCopy non trouvé. Exécutez d'abord: python install_azcopy.py")
        sys.exit(1)
    
    if args.verbose:
        print("🚀 Starting Azure ML data asset download with AzCopy...")
        print(f"📁 Baseline: {args.baseline}")
        print(f"📂 Output directory: {args.output_dir}")
        print(f"🔧 Skip testinference filter: {args.skip_testinference_filter}")
        print(f"⚡ AzCopy path: {azcopy_path}")
        print("-" * 60)
    
    # Authentification Azure
    credential = InteractiveBrowserCredential()
    ml_client = MLClient.from_config(credential=credential)

    # Charger la configuration
    cfg = OmegaConf.load(args.baseline)
    splits = list(cfg.keys())
    baseline_name = args.baseline.split('/')[-1][:-4]
    
    # Calculer le nombre total d'assets
    total_assets = 0
    for split in splits:
        if cfg[split] is not None and len(cfg[split]) > 0:
            if split == 'testinference' and not args.skip_testinference_filter:
                total_assets += max(0, len(cfg[split]) - 17)
            else:
                total_assets += len(cfg[split])
    
    if args.verbose:
        print(f"📊 Total assets to download: {total_assets}")
        print("-" * 60)
    
    current_asset = 0
    successful_downloads = 0
    failed_downloads = 0
    
    for split in splits:
        print(f'🔄 Beginning {split} split...')
        split_folder = os.path.join(args.output_dir, baseline_name, split)
        os.makedirs(split_folder, exist_ok=True)

        # Vérifier si la section est vide
        if cfg[split] is None or len(cfg[split]) == 0:
            print(f"  ⚠️  No assets found in {split} split, skipping...")
            continue

        n_split = len(cfg[split])
        downloaded_in_split = 0
        
        for i_asset, asset_info in enumerate(cfg[split]):
            # Logique de téléchargement
            should_download = True
            skip_reason = ""
            
            if split == 'testinference' and not args.skip_testinference_filter and i_asset < 17:
                should_download = False
                skip_reason = "testinference index < 17"

            if should_download:
                current_asset += 1
                downloaded_in_split += 1
                asset_name, asset_version = asset_info.split(':')
                
                if args.verbose:
                    progress_percent = (current_asset / total_assets) * 100
                    print(f"📦 [{current_asset}/{total_assets}] ({progress_percent:.1f}%) Downloading {asset_name} (v{asset_version})")
                else:
                    print(f"({downloaded_in_split}/{n_split if args.skip_testinference_filter or split != 'testinference' else max(0, n_split-17)})  Downloading {asset_name} (version : {asset_version})")
                
                asset_folder = os.path.join(split_folder, f"{asset_name}_v{asset_version}")

                try:
                    if args.verbose:
                        print(f"  📁 Destination: {asset_folder}")

                    # Obtenir l'URL de blob storage avec SAS
                    blob_url, datastore = get_blob_url_with_sas(ml_client, asset_name, asset_version, args.verbose)

                    if blob_url:
                        # Télécharger avec AzCopy
                        success = download_with_azcopy(blob_url, asset_folder, azcopy_path, args.verbose)

                        if success:
                            successful_downloads += 1
                            if args.verbose:
                                print(f"  ✅ Successfully downloaded to: {asset_folder}")
                        else:
                            failed_downloads += 1
                    else:
                        failed_downloads += 1
                        print(f"  ❌ Could not get blob URL for {asset_name}")

                except Exception as e:
                    failed_downloads += 1
                    print(f"  ❌ Error downloading {asset_name}: {str(e)}")
                    
            else:
                if args.verbose:
                    print(f"⏭️  [{i_asset+1}/{n_split}] Skipping {asset_info} ({skip_reason})")
        
        if args.verbose:
            print(f"✅ Completed {split} split: {downloaded_in_split} assets downloaded")
            print("-" * 40)
    
    # Résumé final
    print(f"\n🎉 Download completed!")
    print(f"✅ Successful downloads: {successful_downloads}")
    if failed_downloads > 0:
        print(f"❌ Failed downloads: {failed_downloads}")
    print(f"📁 Data saved to: {os.path.abspath(args.output_dir)}")

def parse_args():
    parser = argparse.ArgumentParser(description="Download Azure ML data assets using AzCopy")

    parser.add_argument(
        "--baseline",
        type=str,
        default='./boeing_baseline_3.yml',
        help="Path to .yml baseline configuration file"
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default='./data',
        help='Path to export data'
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output with download progress"
    )

    parser.add_argument(
        "--skip-testinference-filter",
        action="store_true",
        help="Download all testinference assets (not just from index 17+)"
    )

    args = parser.parse_args()
    return args

if __name__ == "__main__":
    args = parse_args()
    main(args)
