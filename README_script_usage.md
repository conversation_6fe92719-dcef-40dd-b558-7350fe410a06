# 📋 Guide d'utilisation du script de téléchargement Azure ML

## 🚀 Utilisation de base

```bash
python retrieve_data_asset.py --baseline ./boeing_baseline_3.yml --output_dir ./data
```

## 🔧 Options disponibles

### `--baseline` (requis)
Chemin vers le fichier YAML de configuration des datasets
```bash
--baseline ./boeing_baseline_3.yml
--baseline ./TPI_calibprod_light.yml
```

### `--output_dir` (optionnel, défaut: `./data`)
Dossier de destination pour les données téléchargées
```bash
--output_dir ./data
--output_dir /path/to/my/data
```

### `--verbose` ou `-v` (optionnel)
Active le mode verbose avec progression détaillée
```bash
python retrieve_data_asset.py --baseline ./boeing_baseline_3.yml --verbose
python retrieve_data_asset.py --baseline ./boeing_baseline_3.yml -v
```

**Mode verbose affiche :**
- 🚀 Informations de démarrage
- 📊 Nombre total d'assets à télécharger
- 📦 Progression en temps réel avec pourcentage
- ✅ Confirmation de téléchargement réussi
- ❌ Erreurs de téléchargement
- 🎉 Résumé final

### `--skip-testinference-filter` (optionnel)
Télécharge TOUS les assets testinference (pas seulement à partir de l'index 17)
```bash
python retrieve_data_asset.py --baseline ./boeing_baseline_3.yml --skip-testinference-filter
```

## 📊 Différences entre les fichiers de baseline

### `boeing_baseline_3.yml`
- **Training**: 20 assets
- **Validation**: 20 assets  
- **Testinference**: 44 assets (par défaut, seuls 27 sont téléchargés à partir de l'index 17)

### `TPI_calibprod_light.yml`
- **Training**: 24 assets
- **Validation**: 6 assets
- **Testinference**: vide (aucun téléchargement)

## 🎯 Exemples d'utilisation

### Téléchargement standard (sans avertissements)
```bash
python retrieve_data_asset.py --baseline ./boeing_baseline_3.yml --output_dir ./data
```

### Téléchargement avec progression détaillée
```bash
python retrieve_data_asset.py --baseline ./boeing_baseline_3.yml --output_dir ./data --verbose
```

### Téléchargement de TOUS les assets testinference
```bash
python retrieve_data_asset.py --baseline ./boeing_baseline_3.yml --output_dir ./data --skip-testinference-filter --verbose
```

### Téléchargement TPI avec progression
```bash
python retrieve_data_asset.py --baseline ./TPI_calibprod_light.yml --output_dir ./data --verbose
```

## 📁 Structure de sortie

```
./data/
└── baseline_name/           # Nom du fichier YAML sans extension
    ├── training/            # Assets d'entraînement
    │   ├── asset1_v1/
    │   └── asset2_v2/
    ├── validation/          # Assets de validation
    │   └── asset3_v1/
    └── testinference/       # Assets de test (peut être vide)
        └── asset4_v3/
```

## ⚠️ Pourquoi pas d'avertissement avec boeing_baseline_3.yml ?

L'avertissement "Your file exceeds 100 MB" apparaît seulement quand des fichiers volumineux sont téléchargés. 

- **boeing_baseline_3.yml**: Par défaut, ne télécharge que les assets testinference à partir de l'index 17
- **TPI_calibprod_light.yml**: Télécharge tous les assets training/validation qui sont plus volumineux

## 🛠️ Améliorations apportées

1. ✅ **Suppression des avertissements** de taille de fichier
2. ✅ **Mode verbose** avec progression en temps réel
3. ✅ **Option pour télécharger tous les assets testinference**
4. ✅ **Gestion des sections vides** dans les fichiers YAML
5. ✅ **Gestion d'erreurs améliorée**
6. ✅ **Messages informatifs avec emojis**

## 🧪 Test des options (sans téléchargement)

Pour tester les options sans télécharger :
```bash
python test_script_options.py --baseline ./boeing_baseline_3.yml --verbose
```
