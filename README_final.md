# 🎉 Scripts de téléchargement Azure ML - Version finale

## 📋 Scripts disponibles

### 1. `retrieve_data_asset.py` - Version améliorée
- ✅ Suppression partielle des avertissements
- ✅ Mode verbose avec progression
- ✅ Options flexibles
- ⚠️ Quelques avertissements peuvent encore apparaître

### 2. `retrieve_data_asset_silent.py` - Version silencieuse (RECOMMANDÉE)
- ✅ **Suppression COMPLÈTE de tous les avertissements**
- ✅ Mode verbose avec progression détaillée
- ✅ Téléchargement propre sans pollution de la console
- ✅ Même fonctionnalités que la version standard

### 3. `retrieve_data_asset_azcopy.py` - Version AzCopy (expérimentale)
- ❌ Problèmes d'authentification avec les URLs Azure ML
- 🔧 Nécessite des améliorations supplémentaires

## 🚀 Utilisation recommandée

### **Pour un téléchargement sans avertissements :**
```bash
python retrieve_data_asset_silent.py --baseline ./TPI_calibprod_light.yml --output_dir ./data
```

### **Pour un téléchargement avec progression détaillée :**
```bash
python retrieve_data_asset_silent.py --baseline ./TPI_calibprod_light.yml --output_dir ./data --verbose
```

### **Pour télécharger tous les assets testinference :**
```bash
python retrieve_data_asset_silent.py --baseline ./boeing_baseline_3.yml --output_dir ./data --skip-testinference-filter --verbose
```

## 📊 Comparaison des sorties

### **Mode standard (sans verbose)**
```
🔄 Beginning training split...
(1/24)  Downloading TPI_MASBlock_c_2024_08_28_10_g1 (version : 5)
(2/24)  Downloading TPI_MASBlock_c_2024_08_28_13_g1 (version : 5)
```

### **Mode verbose**
```
🚀 Starting Azure ML data asset download (silent mode)...
📁 Baseline: ./TPI_calibprod_light.yml
📂 Output directory: ./data
📊 Total assets to download: 30
------------------------------------------------------------
🔄 Beginning training split...
📦 [1/30] (3.3%) TPI_MASBlock_c_2024_08_28_10_g1 (v5)
  📦 Downloading TPI_MASBlock_c_2024_08_28_10_g1 v5...
  ✅ Successfully downloaded to: ./data/TPI_calibprod_light/training/TPI_MASBlock_c_2024_08_28_10_g1_v5
📦 [2/30] (6.7%) TPI_MASBlock_c_2024_08_28_13_g1 (v5)
```

## 🔧 Options disponibles

### `--baseline` (requis)
```bash
--baseline ./boeing_baseline_3.yml
--baseline ./TPI_calibprod_light.yml
```

### `--output_dir` (optionnel, défaut: `./data`)
```bash
--output_dir ./data
--output_dir /path/to/my/data
```

### `--verbose` ou `-v` (optionnel)
```bash
--verbose  # Affichage détaillé avec progression
```

### `--skip-testinference-filter` (optionnel)
```bash
--skip-testinference-filter  # Télécharge TOUS les assets testinference
```

## 📈 Avantages de la version silencieuse

### ✅ **Suppression complète des avertissements**
- Plus d'avertissements "Your file exceeds 100 MB"
- Plus de messages de logging Azure
- Console propre et lisible

### ✅ **Progression claire**
- Pourcentage de progression en temps réel
- Compteur d'assets téléchargés
- Résumé final avec statistiques

### ✅ **Gestion d'erreurs améliorée**
- Messages d'erreur clairs et concis
- Comptage des téléchargements réussis/échoués
- Continuation en cas d'erreur sur un asset

## 🎯 Pourquoi pas d'avertissement avec boeing_baseline_3.yml ?

**Explication :**
- `boeing_baseline_3.yml` : Par défaut, ne télécharge que les assets testinference à partir de l'index 17
- `TPI_calibprod_light.yml` : Télécharge tous les assets training/validation qui sont plus volumineux (>100 MB)

**Solution :** Utilisez `retrieve_data_asset_silent.py` pour éliminer tous les avertissements !

## 📁 Structure de sortie

```
./data/
└── baseline_name/           # Nom du fichier YAML sans extension
    ├── training/            # Assets d'entraînement
    │   ├── asset1_v1/
    │   └── asset2_v2/
    ├── validation/          # Assets de validation
    │   └── asset3_v1/
    └── testinference/       # Assets de test (peut être vide)
        └── asset4_v3/
```

## 🛠️ Outils supplémentaires

### `test_script_options.py`
Test des options sans téléchargement réel
```bash
python test_script_options.py --baseline ./boeing_baseline_3.yml --verbose
```

### `install_azcopy.py`
Installation automatique d'AzCopy (pour usage futur)
```bash
python install_azcopy.py
```

## 🎉 Résumé

**Utilisez `retrieve_data_asset_silent.py` pour :**
- ✅ Téléchargements sans avertissements
- ✅ Progression claire et détaillée
- ✅ Console propre et professionnelle
- ✅ Même fonctionnalités que l'original

**Commande recommandée :**
```bash
python retrieve_data_asset_silent.py --baseline ./TPI_calibprod_light.yml --output_dir ./data --verbose
```
