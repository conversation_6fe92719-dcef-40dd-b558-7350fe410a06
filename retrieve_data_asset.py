import argparse
import os
import warnings
from azure.identity import InteractiveBrowserCredential
from azure.ai.ml import MLClient
from azure.ai.ml._utils._storage_utils import get_ds_name_and_path_prefix
import azure.ai.ml._artifacts as Artifacts
from omegaconf import OmegaConf

# Suppress Azure ML warnings about file size
warnings.filterwarnings("ignore", message="Your file exceeds 100 MB")

def main(args):

    if args.verbose:
        print("🚀 Starting Azure ML data asset download...")
        print(f"📁 Baseline: {args.baseline}")
        print(f"📂 Output directory: {args.output_dir}")
        print(f"🔧 Skip testinference filter: {args.skip_testinference_filter}")
        print("-" * 60)

    credential = InteractiveBrowserCredential()
    ml_client = MLClient.from_config(credential=credential)

    cfg = OmegaConf.load(args.baseline)

    splits = list(cfg.keys())
    baseline_name = args.baseline.split('/')[-1][:-4]

    # Calculate total assets for progress tracking
    total_assets = 0
    for split in splits:
        if cfg[split] is not None and len(cfg[split]) > 0:
            if split == 'testinference' and not args.skip_testinference_filter:
                # Only count assets from index 17+
                total_assets += max(0, len(cfg[split]) - 17)
            else:
                total_assets += len(cfg[split])

    if args.verbose:
        print(f"📊 Total assets to download: {total_assets}")
        print("-" * 60)

    current_asset = 0

    for split in splits:
        print(f'🔄 Beginning {split} split...')
        split_folder = os.path.join(args.output_dir, baseline_name, split)
        os.makedirs(split_folder, exist_ok=True)

        # Check if the split section is empty or None
        if cfg[split] is None or len(cfg[split]) == 0:
            print(f"  ⚠️  No assets found in {split} split, skipping...")
            continue

        n_split = len(cfg[split])
        downloaded_in_split = 0

        for i_asset, asset_info in enumerate(cfg[split]):
            # Download logic based on split and filter settings
            should_download = True
            skip_reason = ""

            if split == 'testinference' and not args.skip_testinference_filter and i_asset < 17:
                should_download = False
                skip_reason = "testinference index < 17"

            if should_download:
                current_asset += 1
                downloaded_in_split += 1
                asset_name, asset_version = asset_info.split(':')

                if args.verbose:
                    progress_percent = (current_asset / total_assets) * 100
                    print(f"📦 [{current_asset}/{total_assets}] ({progress_percent:.1f}%) Downloading {asset_name} (v{asset_version})")
                else:
                    print(f"({downloaded_in_split}/{n_split if args.skip_testinference_filter or split != 'testinference' else max(0, n_split-17)})  Downloading {asset_name} (version : {asset_version})")

                asset_folder = os.path.join(split_folder, f"{asset_name}_v{asset_version}")

                try:
                    dataobject = ml_client.data.get(asset_name, version=asset_version)
                    base_path = dataobject.path

                    # Extract datastore information (variables not used but required by the function)
                    _ = get_ds_name_and_path_prefix(base_path)
                    dsobject = ml_client.datastores
                    Artifacts._artifact_utilities.download_artifact_from_aml_uri(uri=base_path, destination=asset_folder, datastore_operation=dsobject)

                    if args.verbose:
                        print(f"  ✅ Successfully downloaded to: {asset_folder}")

                except Exception as e:
                    print(f"  ❌ Error downloading {asset_name}: {str(e)}")

            else:
                if args.verbose:
                    print(f"⏭️  [{i_asset+1}/{n_split}] Skipping {asset_info} ({skip_reason})")

        if args.verbose:
            print(f"✅ Completed {split} split: {downloaded_in_split} assets downloaded")
            print("-" * 40)

    if args.verbose:
        print(f"🎉 Download completed! Total assets downloaded: {current_asset}")
        print(f"📁 Data saved to: {os.path.abspath(args.output_dir)}")



def parse_args():
    # setup argparse
    parser = argparse.ArgumentParser(description="Download Azure ML data assets")

    # add arguments
    parser.add_argument(
        "--baseline",
        type=str,
        default='./boeing_baseline_3.yml',
        help="Path to .yml baseline configuration file"
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default='./data',
        help='Path to export data'
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output with download progress"
    )

    parser.add_argument(
        "--skip-testinference-filter",
        action="store_true",
        help="Download all testinference assets (not just from index 17+)"
    )

    args = parser.parse_args()
    return args

if __name__ == "__main__":
    # parse args
    args = parse_args()

    main(args)