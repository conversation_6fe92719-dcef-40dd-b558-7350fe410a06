#!/usr/bin/env python3
"""
Script de téléchargement Azure ML avec suppression complète des avertissements
"""

import argparse
import os
import sys
import warnings
import contextlib
import io
from azure.identity import InteractiveBrowserCredential
from azure.ai.ml import MLClient
from azure.ai.ml._utils._storage_utils import get_ds_name_and_path_prefix
import azure.ai.ml._artifacts as Artifacts
from omegaconf import OmegaConf

# Suppression complète de tous les avertissements et logs
warnings.filterwarnings("ignore")
import logging
logging.getLogger().setLevel(logging.CRITICAL)
logging.getLogger("azure").setLevel(logging.CRITICAL)
logging.getLogger("azure.storage").setLevel(logging.CRITICAL)
logging.getLogger("azure.ai.ml").setLevel(logging.CRITICAL)
logging.getLogger("urllib3").setLevel(logging.CRITICAL)

@contextlib.contextmanager
def suppress_stdout_stderr():
    """Context manager pour supprimer complètement stdout et stderr"""
    with open(os.devnull, "w") as devnull:
        old_stdout = sys.stdout
        old_stderr = sys.stderr
        try:
            sys.stdout = devnull
            sys.stderr = devnull
            yield
        finally:
            sys.stdout = old_stdout
            sys.stderr = old_stderr

def download_asset_silent(ml_client, asset_name, asset_version, asset_folder, verbose=False):
    """Télécharge un asset en supprimant tous les avertissements"""
    
    try:
        if verbose:
            print(f"  📦 Downloading {asset_name} v{asset_version}...")
        
        # Obtenir les informations de l'asset
        dataobject = ml_client.data.get(asset_name, version=asset_version)
        base_path = dataobject.path
        
        # Télécharger en supprimant tous les outputs
        with suppress_stdout_stderr():
            _ = get_ds_name_and_path_prefix(base_path)
            dsobject = ml_client.datastores
            Artifacts._artifact_utilities.download_artifact_from_aml_uri(
                uri=base_path, 
                destination=asset_folder, 
                datastore_operation=dsobject
            )
        
        if verbose:
            print(f"  ✅ Successfully downloaded to: {asset_folder}")
        
        return True
        
    except Exception as e:
        if verbose:
            print(f"  ❌ Error downloading {asset_name}: {str(e)}")
        return False

def main(args):
    
    if args.verbose:
        print("🚀 Starting Azure ML data asset download (silent mode)...")
        print(f"📁 Baseline: {args.baseline}")
        print(f"📂 Output directory: {args.output_dir}")
        print(f"🔧 Skip testinference filter: {args.skip_testinference_filter}")
        print("-" * 60)
    
    # Authentification Azure (en mode silencieux)
    if not args.verbose:
        with suppress_stdout_stderr():
            credential = InteractiveBrowserCredential()
            ml_client = MLClient.from_config(credential=credential)
    else:
        credential = InteractiveBrowserCredential()
        ml_client = MLClient.from_config(credential=credential)

    # Charger la configuration
    cfg = OmegaConf.load(args.baseline)
    splits = list(cfg.keys())
    baseline_name = args.baseline.split('/')[-1][:-4]
    
    # Calculer le nombre total d'assets
    total_assets = 0
    for split in splits:
        if cfg[split] is not None and len(cfg[split]) > 0:
            if split == 'testinference' and not args.skip_testinference_filter:
                total_assets += max(0, len(cfg[split]) - 17)
            else:
                total_assets += len(cfg[split])
    
    if args.verbose:
        print(f"📊 Total assets to download: {total_assets}")
        print("-" * 60)
    
    current_asset = 0
    successful_downloads = 0
    failed_downloads = 0
    
    for split in splits:
        print(f'🔄 Beginning {split} split...')
        split_folder = os.path.join(args.output_dir, baseline_name, split)
        os.makedirs(split_folder, exist_ok=True)

        # Vérifier si la section est vide
        if cfg[split] is None or len(cfg[split]) == 0:
            print(f"  ⚠️  No assets found in {split} split, skipping...")
            continue

        n_split = len(cfg[split])
        downloaded_in_split = 0
        
        for i_asset, asset_info in enumerate(cfg[split]):
            # Logique de téléchargement
            should_download = True
            skip_reason = ""
            
            if split == 'testinference' and not args.skip_testinference_filter and i_asset < 17:
                should_download = False
                skip_reason = "testinference index < 17"

            if should_download:
                current_asset += 1
                downloaded_in_split += 1
                asset_name, asset_version = asset_info.split(':')
                
                if args.verbose:
                    progress_percent = (current_asset / total_assets) * 100
                    print(f"📦 [{current_asset}/{total_assets}] ({progress_percent:.1f}%) {asset_name} (v{asset_version})")
                else:
                    print(f"({downloaded_in_split}/{n_split if args.skip_testinference_filter or split != 'testinference' else max(0, n_split-17)})  Downloading {asset_name} (version : {asset_version})")
                
                asset_folder = os.path.join(split_folder, f"{asset_name}_v{asset_version}")

                # Télécharger l'asset en mode silencieux
                success = download_asset_silent(ml_client, asset_name, asset_version, asset_folder, args.verbose)
                
                if success:
                    successful_downloads += 1
                else:
                    failed_downloads += 1
                    
            else:
                if args.verbose:
                    print(f"⏭️  [{i_asset+1}/{n_split}] Skipping {asset_info} ({skip_reason})")
        
        if args.verbose:
            print(f"✅ Completed {split} split: {downloaded_in_split} assets downloaded")
            print("-" * 40)
    
    # Résumé final
    print(f"\n🎉 Download completed!")
    print(f"✅ Successful downloads: {successful_downloads}")
    if failed_downloads > 0:
        print(f"❌ Failed downloads: {failed_downloads}")
    print(f"📁 Data saved to: {os.path.abspath(args.output_dir)}")

def parse_args():
    parser = argparse.ArgumentParser(description="Download Azure ML data assets (silent mode)")

    parser.add_argument(
        "--baseline",
        type=str,
        default='./boeing_baseline_3.yml',
        help="Path to .yml baseline configuration file"
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default='./data',
        help='Path to export data'
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose output with download progress"
    )

    parser.add_argument(
        "--skip-testinference-filter",
        action="store_true",
        help="Download all testinference assets (not just from index 17+)"
    )

    args = parser.parse_args()
    return args

if __name__ == "__main__":
    args = parse_args()
    main(args)
